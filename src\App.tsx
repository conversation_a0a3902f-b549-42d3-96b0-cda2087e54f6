import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, useMotionTemplate, useMotionValue, animate } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Send, 
  User, 
  MessageSquare, 
  Building, 
  ArrowRight,
  Sparkles,
  CheckCircle,
  Clock,
  Globe,
  Shield,
  Zap,
  Check,
  Star,
  Menu,
  X,
  TrendingUp,
  BarChart3,
  Target,
  Users,
  Rocket,
  Search,
  FileText,
  Bot,
  Lightbulb,
  DollarSign,
  Award,
  ChevronDown
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarImage } from '@/components/ui/avatar';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

// Utility function for cn
function cnUtil(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

// Navigation Component
const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navItems = [
    { name: 'Features', href: '#features' },
    { name: 'Pricing', href: '#pricing' },
    { name: 'Testimonials', href: '#testimonials' },
    { name: 'FAQ', href: '#faq' },
    { name: 'Contact', href: '#contact' }
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Rocket className="h-8 w-8 text-blue-600 mr-2" />
            <span className="text-xl font-bold text-gray-900">MarTech AI</span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="text-gray-600 hover:text-blue-600 transition-colors duration-200"
              >
                {item.name}
              </a>
            ))}
          </div>

          {/* Desktop CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
              Sign In
            </Button>
            <Button size="sm" className="bg-blue-600 text-white hover:bg-blue-700">
              Start Free Trial
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-md text-white hover:text-white hover:bg-white/10"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden border-t border-white/20"
              style={{ backgroundColor: '#020617' }}
            >
              <div className="px-2 pt-2 pb-3 space-y-1">
                {navItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="block px-3 py-2 text-white/80 hover:text-white hover:bg-white/10 rounded-md transition-colors duration-200"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </a>
                ))}
                <div className="pt-4 space-y-2">
                  <Button variant="ghost" size="sm" className="w-full justify-start text-white hover:text-white hover:bg-white/10">
                    Sign In
                  </Button>
                  <Button size="sm" className="w-full bg-white text-black hover:bg-gray-100">
                    Start Free Trial
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </nav>
  );
};

// Hero Section Component
const MarTechHero = () => {
  const color = useMotionValue("#13FFAA");
  
  useEffect(() => {
    animate(color, ["#13FFAA", "#1E67C6", "#CE84CF", "#DD335C"], {
      ease: "easeInOut",
      duration: 10,
      repeat: Infinity,
      repeatType: "mirror",
    });
  }, [color]);

  const backgroundImage = useMotionTemplate`radial-gradient(125% 125% at 50% 0%, #020617 50%, ${color})`;

  return (
    <motion.section
      style={{ backgroundImage }}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gray-950 px-4 pt-32 pb-24 text-gray-200"
    >
      <div className="relative z-10 flex flex-col items-center text-center max-w-6xl mx-auto">
        <Badge className="mb-6 bg-white/10 text-white border-white/20">
          ✨ AI-Powered MarTech Suite
        </Badge>
        
        <h1 className="max-w-4xl bg-gradient-to-br from-white to-gray-400 bg-clip-text text-center text-4xl font-bold leading-relaxed text-transparent sm:text-6xl md:text-7xl py-4">
          Scale Your Content Marketing with AI
        </h1>
        
        <p className="my-8 max-w-2xl text-center text-lg leading-relaxed text-gray-300 md:text-xl">
          Automate SEO content creation, boost organic traffic, and convert visitors into customers. 
          Perfect for indie developers, e-commerce professionals, and website owners.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 items-center">
          <Button size="lg" className="bg-white text-black hover:bg-gray-100">
            Start Free Trial
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          <Button size="lg" variant="outline" className="border-white/30 text-white hover:bg-white/10 hover:text-white bg-transparent">
            Watch Demo
          </Button>
        </div>

        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {[
            { icon: TrendingUp, label: "500% Traffic Boost", desc: "Average increase" },
            { icon: Clock, label: "10x Faster", desc: "Content creation" },
            { icon: Users, label: "50K+ Users", desc: "Trust our platform" },
            { icon: Award, label: "99.9% Uptime", desc: "Reliable service" }
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 + 0.5 }}
              className="text-center"
            >
              <stat.icon className="h-8 w-8 mx-auto mb-2 text-blue-400" />
              <div className="text-xl font-bold text-white">{stat.label}</div>
              <div className="text-sm text-gray-400">{stat.desc}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
};

// Social Proof Component
const SocialProof = () => {
  const logos = [
    "Shopify", "Amazon", "eBay", "WordPress", "Webflow", "Squarespace"
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 text-center">
        <p className="text-gray-600 mb-8">Trusted by 50,000+ developers and marketers worldwide</p>
        <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
          {logos.map((logo, index) => (
            <div key={index} className="text-2xl font-bold text-gray-400">
              {logo}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Features Component
const Features = () => {
  const features = [
    {
      icon: Search,
      title: "AI SEO Content Generator",
      description: "Generate high-ranking blog posts, product descriptions, and landing pages with our advanced AI engine.",
      benefits: ["Keyword optimization", "SERP analysis", "Content scoring"]
    },
    {
      icon: Bot,
      title: "Marketing Automation",
      description: "Automate your entire content marketing workflow from ideation to publication and promotion.",
      benefits: ["Auto-publishing", "Social media scheduling", "Email campaigns"]
    },
    {
      icon: BarChart3,
      title: "Performance Analytics",
      description: "Track your content performance, monitor rankings, and optimize for better conversions.",
      benefits: ["Traffic analytics", "Conversion tracking", "ROI reporting"]
    },
    {
      icon: Target,
      title: "Cross-Border E-commerce Tools",
      description: "Specialized tools for Amazon, eBay, and Shopify sellers to optimize international listings.",
      benefits: ["Multi-language content", "Currency optimization", "Local SEO"]
    },
    {
      icon: FileText,
      title: "Content Templates",
      description: "Pre-built templates for every content type - from blog posts to product descriptions.",
      benefits: ["Industry-specific templates", "Custom branding", "A/B testing"]
    },
    {
      icon: Lightbulb,
      title: "Competitor Analysis",
      description: "Analyze competitor content strategies and discover new opportunities in your niche.",
      benefits: ["Gap analysis", "Keyword research", "Content ideas"]
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <Badge className="mb-4">Features</Badge>
          <h2 className="text-4xl font-bold mb-4">Everything You Need to Scale</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Comprehensive MarTech tools designed specifically for developers, e-commerce professionals, and content creators.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              viewport={{ once: true }}
              className="p-6 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all"
            >
              <feature.icon className="h-12 w-12 text-blue-600 mb-4" />
              <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
              <p className="text-gray-600 mb-4">{feature.description}</p>
              <ul className="space-y-2">
                {feature.benefits.map((benefit, i) => (
                  <li key={i} className="flex items-center text-sm text-gray-500">
                    <Check className="h-4 w-4 text-green-500 mr-2" />
                    {benefit}
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Pricing Component
const Pricing = () => {
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      name: "Starter",
      description: "Perfect for indie developers and small projects",
      price: { monthly: 29, yearly: 290 },
      features: [
        "10 AI-generated articles/month",
        "Basic SEO optimization",
        "1 website integration",
        "Email support",
        "Content templates"
      ],
      popular: false
    },
    {
      name: "Professional",
      description: "Ideal for growing e-commerce businesses",
      price: { monthly: 79, yearly: 790 },
      features: [
        "100 AI-generated articles/month",
        "Advanced SEO & analytics",
        "5 website integrations",
        "Priority support",
        "A/B testing",
        "Competitor analysis",
        "Multi-language support"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      description: "For agencies and large organizations",
      price: { monthly: 199, yearly: 1990 },
      features: [
        "Unlimited AI content",
        "White-label solution",
        "Unlimited integrations",
        "Dedicated account manager",
        "Custom integrations",
        "Advanced analytics",
        "Team collaboration"
      ],
      popular: false
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <Badge className="mb-4">Pricing</Badge>
          <h2 className="text-4xl font-bold mb-4">Simple, Transparent Pricing</h2>
          <p className="text-xl text-gray-600 mb-8">Choose the plan that fits your needs. Upgrade or downgrade anytime.</p>

          <div className="flex items-center justify-center gap-4 mb-8">
            <span className={cn("text-sm", !isYearly ? "font-semibold" : "text-gray-500")}>Monthly</span>
            <button
              onClick={() => setIsYearly(!isYearly)}
              className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                  isYearly ? "translate-x-6" : "translate-x-1"
                )}
              />
            </button>
            <span className={cn("text-sm", isYearly ? "font-semibold" : "text-gray-500")}>
              Yearly <Badge className="ml-1 bg-green-100 text-green-800">Save 17%</Badge>
            </span>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <Card key={index} className={cn(
              "p-8 relative",
              plan.popular ? "border-blue-500 border-2 shadow-lg scale-105" : "border-gray-200"
            )}>
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500">
                  Most Popular
                </Badge>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-4">{plan.description}</p>
                <div className="mb-4">
                  <span className="text-4xl font-bold">
                    ${isYearly ? plan.price.yearly : plan.price.monthly}
                  </span>
                  <span className="text-gray-500">
                    /{isYearly ? "year" : "month"}
                  </span>
                </div>
              </div>

              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, i) => (
                  <li key={i} className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                className={cn(
                  "w-full",
                  plan.popular ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-900 hover:bg-gray-800"
                )}
              >
                Get Started
              </Button>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

// Testimonials Component
const TestimonialsSection = () => {
  const testimonials = [
    {
      author: {
        name: "Sarah Chen",
        handle: "@sarahdev",
        avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face"
      },
      text: "Increased our organic traffic by 400% in just 3 months. The AI content generator is incredibly accurate and saves us 20+ hours per week.",
      company: "SaaS Startup Founder"
    },
    {
      author: {
        name: "Marcus Rodriguez",
        handle: "@marcustech",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
      },
      text: "As an Amazon seller, this platform helped me optimize my product listings for international markets. Sales increased by 250%.",
      company: "E-commerce Professional"
    },
    {
      author: {
        name: "Emily Watson",
        handle: "@emilyblogs",
        avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face"
      },
      text: "The content automation features are game-changing. I can now focus on strategy while the AI handles content creation and optimization.",
      company: "Content Marketing Manager"
    },
    {
      author: {
        name: "David Kim",
        handle: "@davidcodes",
        avatar: "https://images.unsplash.com/photo-1636041293178-808a6762ab39?w=150&h=150&fit=crop&crop=face"
      },
      text: "Perfect for developers who need content marketing but don't have the time. The technical accuracy of AI-generated content is impressive.",
      company: "Indie Developer"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <Badge className="mb-4">Testimonials</Badge>
          <h2 className="text-4xl font-bold mb-4">Loved by Developers & Marketers</h2>
          <p className="text-xl text-gray-600">See how our platform is helping businesses scale their content marketing.</p>
        </div>

        <div className="relative overflow-hidden">
          <div className="flex animate-marquee space-x-6">
            {[...testimonials, ...testimonials].map((testimonial, index) => (
              <Card key={index} className="flex-shrink-0 w-80 p-6">
                <div className="flex items-center mb-4">
                  <Avatar className="h-12 w-12 mr-3">
                    <AvatarImage src={testimonial.author.avatar} alt={testimonial.author.name} />
                  </Avatar>
                  <div>
                    <h4 className="font-semibold">{testimonial.author.name}</h4>
                    <p className="text-sm text-gray-500">{testimonial.company}</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">{testimonial.text}</p>
                <div className="flex mt-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

// FAQ Component
const FAQ = () => {
  const faqs = [
    {
      question: "How does the AI content generator work?",
      answer: "Our AI analyzes your target keywords, competitor content, and industry trends to generate high-quality, SEO-optimized content that ranks well in search engines."
    },
    {
      question: "Can I use this for e-commerce product descriptions?",
      answer: "Absolutely! Our platform includes specialized templates for e-commerce, including Amazon listings, Shopify product descriptions, and international market optimization."
    },
    {
      question: "Is there a free trial available?",
      answer: "Yes, we offer a 14-day free trial with access to all features. No credit card required to start."
    },
    {
      question: "How accurate is the AI-generated content?",
      answer: "Our AI is trained on millions of high-performing content pieces and maintains 95%+ accuracy. All content is fact-checked and optimized for your specific industry."
    },
    {
      question: "Can I integrate with my existing tools?",
      answer: "Yes, we integrate with 50+ popular tools including WordPress, Shopify, HubSpot, Google Analytics, and more. Custom integrations are available for Enterprise plans."
    },
    {
      question: "What kind of support do you provide?",
      answer: "We offer email support for all plans, priority support for Professional plans, and dedicated account management for Enterprise customers."
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <Badge className="mb-4">FAQ</Badge>
          <h2 className="text-4xl font-bold mb-4">Frequently Asked Questions</h2>
          <p className="text-xl text-gray-600">Everything you need to know about our platform.</p>
        </div>

        <Accordion type="single" collapsible className="space-y-4">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`} className="bg-white rounded-lg border">
              <AccordionTrigger className="px-6 py-4 text-left font-semibold hover:no-underline">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="px-6 pb-4 text-gray-600">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
};

// Contact Component
const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <section className="py-24 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12">
          <div>
            <Badge className="mb-4">Contact Us</Badge>
            <h2 className="text-4xl font-bold mb-4">Ready to Scale Your Content Marketing?</h2>
            <p className="text-xl text-gray-600 mb-8">
              Get in touch with our team to learn how we can help you automate and optimize your content strategy.
            </p>

            <div className="space-y-6">
              {[
                { icon: Mail, title: "Email", value: "<EMAIL>", href: "mailto:<EMAIL>" },
                { icon: Phone, title: "Phone", value: "+****************", href: "tel:+15551234567" },
                { icon: MapPin, title: "Office", value: "San Francisco, CA", href: "#" }
              ].map((contact, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <contact.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">{contact.title}</h4>
                    <a href={contact.href} className="text-gray-600 hover:text-blue-600">
                      {contact.value}
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Card className="p-8">
            <AnimatePresence mode="wait">
              {!isSubmitted ? (
                <motion.form
                  key="form"
                  onSubmit={handleSubmit}
                  className="space-y-6"
                  initial={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      value={formData.company}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      rows={4}
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      required
                    />
                  </div>

                  <Button type="submit" className="w-full" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <div className="flex items-center">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                        Sending...
                      </div>
                    ) : (
                      <>
                        Send Message
                        <Send className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </motion.form>
              ) : (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-8"
                >
                  <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold mb-2">Message Sent!</h3>
                  <p className="text-gray-600 mb-6">
                    Thank you for reaching out. We'll get back to you within 24 hours.
                  </p>
                  <Button
                    onClick={() => {
                      setIsSubmitted(false);
                      setFormData({ name: '', email: '', company: '', message: '' });
                    }}
                    variant="outline"
                  >
                    Send Another Message
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </Card>
        </div>
      </div>
    </section>
  );
};

// Footer Component
const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white py-16">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          <div className="col-span-2">
            <div className="flex items-center mb-4">
              <Rocket className="h-8 w-8 text-blue-400 mr-2" />
              <span className="text-2xl font-bold">MarTech AI</span>
            </div>
            <p className="text-gray-400 mb-6 max-w-md">
              Automate your content marketing with AI. Perfect for developers, e-commerce professionals, and content creators.
            </p>
            <div className="flex space-x-4">
              {['Twitter', 'LinkedIn', 'GitHub'].map((social) => (
                <a key={social} href="#" className="text-gray-400 hover:text-white">
                  {social}
                </a>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Product</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white">Features</a></li>
              <li><a href="#" className="hover:text-white">Pricing</a></li>
              <li><a href="#" className="hover:text-white">API</a></li>
              <li><a href="#" className="hover:text-white">Integrations</a></li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4">Support</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white">Documentation</a></li>
              <li><a href="#" className="hover:text-white">Help Center</a></li>
              <li><a href="#" className="hover:text-white">Contact</a></li>
              <li><a href="#" className="hover:text-white">Status</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 MarTech AI. All rights reserved.
          </p>
          <div className="flex space-x-6 text-sm text-gray-400 mt-4 md:mt-0">
            <a href="#" className="hover:text-white">Privacy Policy</a>
            <a href="#" className="hover:text-white">Terms of Service</a>
            <a href="#" className="hover:text-white">Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

// Main MarTech Website Component
const MarTechWebsite = () => {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      <MarTechHero />
      <SocialProof />
      <div id="features">
        <Features />
      </div>
      <div id="pricing">
        <Pricing />
      </div>
      <div id="testimonials">
        <TestimonialsSection />
      </div>
      <div id="faq">
        <FAQ />
      </div>
      <div id="contact">
        <Contact />
      </div>
      <Footer />
    </div>
  );
};

export default MarTechWebsite;
